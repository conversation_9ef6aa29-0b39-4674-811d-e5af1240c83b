"""
简单测试 - 验证Excel操作
"""
import sys
import os
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def safe_print(text):
    try:
        print(text, flush=True)
    except:
        print("[编码处理]", flush=True)

def test_excel_direct():
    """直接测试Excel工具"""
    safe_print("🔧 直接测试Excel工具...")
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        tool = create_excel_writer_tool()
        result = tool._run(
            customer_id="TEST001",
            product_id="TEST002", 
            agreement_price=99.99
        )
        
        safe_print("✅ Excel工具测试成功")
        safe_print(result)
        return True
        
    except Exception as e:
        safe_print(f"❌ Excel工具测试失败: {e}")
        return False

def check_data():
    """检查数据"""
    try:
        import pandas as pd
        df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
        safe_print(f"📊 当前数据行数: {len(df)}")
        return len(df)
    except Exception as e:
        safe_print(f"❌ 检查数据失败: {e}")
        return 0

if __name__ == '__main__':
    safe_print("🚀 简单测试")
    
    before = check_data()
    success = test_excel_direct()
    after = check_data()
    
    if success and after > before:
        safe_print("✅ 测试成功！数据已写入Excel")
    else:
        safe_print("❌ 测试失败")
    
    safe_print(f"数据行数变化: {before} → {after}")
