"""
使用测试文件的启动脚本
"""
import sys
import os
from pathlib import Path

print("🚀 启动 Data Agent 测试文件版本...", flush=True)

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

print("✅ 环境初始化完成", flush=True)

def safe_print(text):
    """安全打印"""
    try:
        print(text, flush=True)
    except UnicodeEncodeError:
        # 如果编码失败，使用ASCII安全模式
        safe_text = str(text).encode('ascii', 'ignore').decode('ascii')
        print(safe_text, flush=True)
    except Exception as e:
        print(f"[输出错误: {e}]", flush=True)

def prepare_test_file():
    """准备测试文件"""
    try:
        import shutil

        original_file = Path("data/raw/底表.xlsx")
        test_file = Path("data/raw/底表_测试.xlsx")

        safe_print(f"检查文件: {original_file}")
        safe_print(f"目标文件: {test_file}")

        if original_file.exists():
            if not test_file.exists():
                shutil.copy2(original_file, test_file)
                safe_print(f"已创建测试文件: {test_file}")
            else:
                safe_print(f"测试文件已存在: {test_file}")
            return test_file
        else:
            safe_print(f"原始文件不存在: {original_file}")
            return None

    except Exception as e:
        safe_print(f"准备测试文件失败: {e}")
        return None

def test_user_command():
    """直接测试用户的指令"""
    safe_print("直接测试用户指令: 客户ID为002 货品ID为003 价格为3")

    try:
        safe_print("导入必要的模块...")
        import pandas as pd
        from datetime import datetime, timedelta
        safe_print("模块导入成功")
        test_file = Path("data/raw/底表_测试.xlsx")
        safe_print(f"使用测试文件: {test_file}")

        if not test_file.exists():
            safe_print(f"测试文件不存在: {test_file}")
            return False
        
        # 读取现有数据
        df = pd.read_excel(test_file, sheet_name="协议价集合")
        safe_print(f"原始数据行数: {len(df)}")
        
        # 准备新数据
        current_date = datetime.now()
        start_date = current_date.strftime("%Y/%m/%d")
        end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
        
        new_row = {}
        for col in df.columns:
            if col == "客户ID(必填)":
                new_row[col] = "002"
            elif col == "货品ID(必填)":
                new_row[col] = "003"
            elif col == "协议价(必填)":
                new_row[col] = 3.0
            elif col == "开始日期(必填)":
                new_row[col] = start_date
            elif col == "结束日期(必填)":
                new_row[col] = end_date
            elif col == "考核价":
                new_row[col] = 3.0
            elif col == "建议零售价":
                new_row[col] = 3.0
            else:
                new_row[col] = ""
        
        # 添加新行
        new_df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
        
        # 保存所有工作表
        all_sheets = {}
        with pd.ExcelFile(test_file) as xls:
            for sheet_name in xls.sheet_names:
                if sheet_name == "协议价集合":
                    all_sheets[sheet_name] = new_df
                else:
                    all_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
        
        # 写入文件
        with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
            for sheet_name, sheet_data in all_sheets.items():
                sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        safe_print("协议价数据已成功写入Excel文件！")
        safe_print("")
        safe_print("写入详情：")
        safe_print(f"• 文件路径：{test_file}")
        safe_print("• 工作表：协议价集合")
        safe_print("• 客户ID：002")
        safe_print("• 货品ID：003")
        safe_print("• 协议价：3.0")
        safe_print(f"• 开始日期：{start_date}")
        safe_print(f"• 结束日期：{end_date}")
        safe_print("• 考核价：3.0")
        safe_print("• 建议零售价：3.0")
        safe_print("")
        safe_print(f"当前工作表共有 {len(new_df)} 条记录。")
        
        return True
        
    except Exception as e:
        safe_print(f"测试失败: {e}")
        return False

def create_simple_agent():
    """创建简化的智能体"""
    safe_print("创建智能体...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        
        # 加载环境变量
        load_dotenv()
        
        # 禁用追踪
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.1
        )
        
        # 创建一个简单的工具
        from langchain.tools import BaseTool
        from pydantic import BaseModel, Field
        from typing import Type
        
        class SimpleExcelInput(BaseModel):
            customer_id: str = Field(description="客户ID")
            product_id: str = Field(description="货品ID")
            agreement_price: float = Field(description="协议价")
        
        class SimpleExcelTool(BaseTool):
            name: str = "write_to_test_excel"
            description: str = "写入协议价数据到测试Excel文件"
            args_schema: Type[BaseModel] = SimpleExcelInput
            
            def _run(self, customer_id: str, product_id: str, agreement_price: float) -> str:
                try:
                    # 使用测试文件
                    import pandas as pd
                    from datetime import datetime, timedelta
                    
                    test_file = Path("data/raw/底表_测试.xlsx")
                    
                    # 读取现有数据
                    df = pd.read_excel(test_file, sheet_name="协议价集合")
                    
                    # 准备新数据
                    current_date = datetime.now()
                    start_date = current_date.strftime("%Y/%m/%d")
                    end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
                    
                    new_row = {}
                    for col in df.columns:
                        if col == "客户ID(必填)":
                            new_row[col] = customer_id
                        elif col == "货品ID(必填)":
                            new_row[col] = product_id
                        elif col == "协议价(必填)":
                            new_row[col] = agreement_price
                        elif col == "开始日期(必填)":
                            new_row[col] = start_date
                        elif col == "结束日期(必填)":
                            new_row[col] = end_date
                        elif col == "考核价":
                            new_row[col] = agreement_price
                        elif col == "建议零售价":
                            new_row[col] = agreement_price
                        else:
                            new_row[col] = ""
                    
                    # 添加新行
                    new_df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
                    
                    # 保存所有工作表
                    all_sheets = {}
                    with pd.ExcelFile(test_file) as xls:
                        for sheet_name in xls.sheet_names:
                            if sheet_name == "协议价集合":
                                all_sheets[sheet_name] = new_df
                            else:
                                all_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
                    
                    # 写入文件
                    with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
                        for sheet_name, sheet_data in all_sheets.items():
                            sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    return f"成功写入数据到测试文件！客户ID: {customer_id}, 货品ID: {product_id}, 价格: {agreement_price}"
                    
                except Exception as e:
                    return f"写入失败: {str(e)}"
        
        # 创建工具
        excel_tool = SimpleExcelTool()
        tools = [excel_tool]
        
        # 简单提示词
        system_prompt = """
        You process agreement price commands.
        
        When you see "客户ID为", "货品ID为", "价格为", "开始执行脚本":
        Extract the values and call write_to_test_excel tool.
        
        Example: "客户ID为002 货品ID为003 价格为3 开始执行脚本"
        Extract: customer_id="002", product_id="003", agreement_price=3
        
        Respond in Chinese.
        """
        
        # 创建智能体
        agent = create_react_agent(model=llm, tools=tools, prompt=system_prompt)
        
        safe_print("智能体创建成功！")
        return agent
        
    except Exception as e:
        safe_print(f"智能体创建失败: {e}")
        return None

def main():
    """主函数"""
    safe_print("Data Agent 测试文件版本")
    safe_print("=" * 50)

    try:
        # 准备测试文件
        safe_print("步骤1: 准备测试文件...")
        test_file = prepare_test_file()
        if not test_file:
            safe_print("测试文件准备失败")
            return

        safe_print(f"✅ 测试文件准备完成: {test_file}")
    except Exception as e:
        safe_print(f"❌ 准备阶段失败: {e}")
        return
    
    try:
        # 直接测试用户指令
        safe_print("\n步骤2: 直接测试用户指令...")
        direct_success = test_user_command()

        if direct_success:
            safe_print("\n步骤3: 创建智能体进行交互测试...")
            agent = create_simple_agent()
        else:
            safe_print("直接测试失败，跳过智能体创建")
            return
    except Exception as e:
        safe_print(f"测试阶段失败: {e}")
        return
        
        if agent:
            safe_print("\n触发指令示例:")
            safe_print("客户ID为002 货品ID为003 价格为3 开始执行脚本")
            
            safe_print("\n交互模式 (输入 'quit' 退出):")
            
            while True:
                try:
                    user_input = input("\n用户: ").strip()
                    if user_input.lower() in ['quit', 'exit']:
                        safe_print("再见！")
                        break
                    
                    if not user_input:
                        continue
                    
                    safe_print("Agent: 处理中...")
                    
                    try:
                        response = agent.invoke({
                            "messages": [{"role": "user", "content": user_input}]
                        })
                        
                        result = response["messages"][-1].content
                        safe_print(result)
                        
                    except Exception as e:
                        safe_print(f"处理错误: {str(e)}")
                        
                except KeyboardInterrupt:
                    safe_print("\n再见！")
                    break
        else:
            safe_print("智能体创建失败，但直接测试成功")
    else:
        safe_print("直接测试失败")

if __name__ == '__main__':
    main()
