"""
编码安全的启动脚本
专门解决: 'ascii' codec can't encode characters in position 47-54: ordinal not in range(128)
"""
import sys
import os
from pathlib import Path

# 强制设置编码环境
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'

# 重新配置标准输出（如果支持）
if hasattr(sys.stdout, 'reconfigure'):
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except:
        pass

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def safe_print(text):
    """安全打印函数，避免编码错误"""
    try:
        print(text, flush=True)
    except UnicodeEncodeError:
        # 编码失败时的处理
        try:
            # 尝试使用替换模式
            safe_text = str(text).encode('ascii', 'replace').decode('ascii')
            print(safe_text, flush=True)
        except:
            print("[编码错误，内容无法显示]", flush=True)
    except Exception as e:
        print(f"[输出错误: {e}]", flush=True)

def create_encoding_safe_agent():
    """创建编码安全的智能体"""
    safe_print("🤖 创建编码安全的智能体...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.1
        )
        
        # 使用Excel工具
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        # 强化的系统提示词
        system_prompt = """
        You are an Excel data processing assistant. Your ONLY job is to detect trigger commands and execute Excel operations.

        CRITICAL RULE: When you see ANY text containing ALL of these elements:
        - "客户ID为" followed by any value
        - "货品ID为" followed by any value
        - "价格为" followed by any number
        - "开始执行脚本"

        You MUST immediately call the write_agreement_price_to_excel tool.

        EXTRACTION RULES:
        1. Find "客户ID为" and extract the value after it (until comma, space, or Chinese punctuation)
        2. Find "货品ID为" and extract the value after it (until comma, space, or Chinese punctuation)
        3. Find "价格为" and extract the number after it
        4. Call write_agreement_price_to_excel with: customer_id, product_id, agreement_price

        EXAMPLES that MUST trigger the tool:
        - "客户ID为001,货品ID为002,价格为1,开始执行脚本" → call tool with customer_id="001", product_id="002", agreement_price=1.0
        - "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本" → call tool with customer_id="C001", product_id="P001", agreement_price=99.99

        DO NOT ask questions. DO NOT hesitate. Execute the tool call immediately when you see the pattern.
        Always respond in English: "Data saved successfully. Customer: [ID], Product: [ID], Price: [amount]"
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        safe_print("✅ 智能体创建成功")
        return agent
        
    except Exception as e:
        safe_print(f"❌ 智能体创建失败: {e}")
        return None

def safe_agent_invoke(agent, user_input):
    """安全调用智能体"""
    try:
        response = agent.invoke({
            "messages": [{"role": "user", "content": user_input}]
        })
        
        result = response["messages"][-1].content
        safe_print(result)
        
    except UnicodeEncodeError:
        safe_print("⚠️ 响应包含特殊字符，但处理可能已完成")
        safe_print("请检查Excel文件以确认操作结果")
    except Exception as e:
        safe_print(f"❌ 处理错误: {e}")

def main():
    """主函数"""
    safe_print("🚀 编码安全的Data Agent")
    safe_print("=" * 50)
    safe_print("解决方案: 'ascii' codec can't encode characters 错误")
    safe_print("=" * 50)
    
    # 创建智能体
    agent = create_encoding_safe_agent()
    if not agent:
        safe_print("❌ 智能体创建失败，程序退出")
        return
    
    # 显示使用说明
    safe_print("\n📋 使用说明:")
    safe_print("✅ 触发格式: 客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本")
    safe_print("✅ 智能体将使用英文响应避免编码问题")
    safe_print("✅ 输入 'quit' 退出程序")
    safe_print("-" * 50)
    
    # 交互循环
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                safe_print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            safe_print("🤖 Agent: 处理中...")
            safe_agent_invoke(agent, user_input)
            
        except KeyboardInterrupt:
            safe_print("\n👋 再见！")
            break
        except Exception as e:
            safe_print(f"❌ 输入错误: {e}")

if __name__ == '__main__':
    main()
