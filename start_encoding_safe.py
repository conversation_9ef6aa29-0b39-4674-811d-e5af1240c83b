"""
编码安全的启动脚本
专门解决: 'ascii' codec can't encode characters in position 47-54: ordinal not in range(128)
"""
import sys
import os
from pathlib import Path

# 强制设置编码环境
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'

# 重新配置标准输出（如果支持）
if hasattr(sys.stdout, 'reconfigure'):
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except:
        pass

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def safe_print(text):
    """安全打印函数，避免编码错误"""
    try:
        print(text, flush=True)
    except UnicodeEncodeError:
        # 编码失败时的处理
        try:
            # 尝试使用替换模式
            safe_text = str(text).encode('ascii', 'replace').decode('ascii')
            print(safe_text, flush=True)
        except:
            print("[编码错误，内容无法显示]", flush=True)
    except Exception as e:
        print(f"[输出错误: {e}]", flush=True)

def create_encoding_safe_agent():
    """创建编码安全的智能体"""
    safe_print("🤖 创建编码安全的智能体...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.1
        )
        
        # 使用Excel工具
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        # 系统提示词（使用英文避免编码问题）
        system_prompt = """
        You are a helpful assistant that processes agreement price data.
        
        When you see the exact format: "客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本"
        You MUST:
        1. Extract customer_id (after 客户ID为)
        2. Extract product_id (after 货品ID为)
        3. Extract agreement_price (after 价格为)
        4. Call write_agreement_price_to_excel tool immediately
        
        IMPORTANT: Always respond in simple English to avoid encoding issues.
        Example response: "Data processed successfully. Customer: C001, Product: P001, Price: 99.99"
        
        For non-trigger commands, respond briefly in English.
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        safe_print("✅ 智能体创建成功")
        return agent
        
    except Exception as e:
        safe_print(f"❌ 智能体创建失败: {e}")
        return None

def safe_agent_invoke(agent, user_input):
    """安全调用智能体"""
    try:
        response = agent.invoke({
            "messages": [{"role": "user", "content": user_input}]
        })
        
        result = response["messages"][-1].content
        safe_print(result)
        
    except UnicodeEncodeError:
        safe_print("⚠️ 响应包含特殊字符，但处理可能已完成")
        safe_print("请检查Excel文件以确认操作结果")
    except Exception as e:
        safe_print(f"❌ 处理错误: {e}")

def main():
    """主函数"""
    safe_print("🚀 编码安全的Data Agent")
    safe_print("=" * 50)
    safe_print("解决方案: 'ascii' codec can't encode characters 错误")
    safe_print("=" * 50)
    
    # 创建智能体
    agent = create_encoding_safe_agent()
    if not agent:
        safe_print("❌ 智能体创建失败，程序退出")
        return
    
    # 显示使用说明
    safe_print("\n📋 使用说明:")
    safe_print("✅ 触发格式: 客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本")
    safe_print("✅ 智能体将使用英文响应避免编码问题")
    safe_print("✅ 输入 'quit' 退出程序")
    safe_print("-" * 50)
    
    # 交互循环
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                safe_print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            safe_print("🤖 Agent: 处理中...")
            safe_agent_invoke(agent, user_input)
            
        except KeyboardInterrupt:
            safe_print("\n👋 再见！")
            break
        except Exception as e:
            safe_print(f"❌ 输入错误: {e}")

if __name__ == '__main__':
    main()
