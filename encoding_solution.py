"""
编码问题解决方案
专门解决: 'ascii' codec can't encode characters in position 47-54: ordinal not in range(128)
"""
import sys
import os
from pathlib import Path

# 设置编码环境
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def safe_print(text):
    """安全打印函数"""
    try:
        print(text, flush=True)
    except UnicodeEncodeError:
        # 编码失败时的安全处理
        safe_text = str(text).encode('ascii', 'ignore').decode('ascii')
        print(f"[编码处理] {safe_text}", flush=True)
    except Exception as e:
        print(f"[输出错误] {e}", flush=True)

def create_encoding_safe_agent():
    """创建编码安全的智能体"""
    safe_print("🤖 创建编码安全的智能体...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.1
        )
        
        # 使用Excel工具
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        # 使用英文系统提示词避免编码问题
        system_prompt = """
        You are a helpful assistant that processes agreement price data.
        
        When you see the exact format: "客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本"
        You MUST:
        1. Extract customer_id (after 客户ID为)
        2. Extract product_id (after 货品ID为)
        3. Extract agreement_price (after 价格为)
        4. Call write_agreement_price_to_excel tool immediately
        
        Always respond with simple English confirmation to avoid encoding issues.
        Example response: "Data processed successfully. Customer: XXX, Product: XXX, Price: XXX"
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        safe_print("✅ 智能体创建成功")
        return agent
        
    except Exception as e:
        safe_print(f"❌ 智能体创建失败: {e}")
        return None

def test_with_encoding_safe_agent():
    """使用编码安全智能体进行测试"""
    safe_print("\n🧪 开始编码安全测试...")
    
    agent = create_encoding_safe_agent()
    if not agent:
        return False
    
    # 测试用例
    test_cases = [
        {
            "name": "触发测试",
            "input": "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本",
            "should_trigger": True
        },
        {
            "name": "非触发测试",
            "input": "客户ID是C001，货品ID是P001，价格是99.99",
            "should_trigger": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        safe_print(f"\n🔄 测试 {i}: {test_case['name']}")
        safe_print(f"💬 输入: {test_case['input']}")
        safe_print("🤖 响应:")
        safe_print("-" * 30)
        
        try:
            response = agent.invoke({
                "messages": [{"role": "user", "content": test_case['input']}]
            })
            
            # 安全处理响应
            result = response["messages"][-1].content
            safe_print(result)
            safe_print("-" * 30)
            safe_print("✅ 测试完成")
            
        except UnicodeEncodeError:
            safe_print("⚠️ 编码错误已捕获，但操作可能已完成")
            safe_print("请检查Excel文件以确认数据是否已写入")
        except Exception as e:
            safe_print(f"❌ 测试失败: {e}")
    
    return True

def verify_excel_file():
    """验证Excel文件"""
    safe_print("\n📊 验证Excel文件...")
    
    try:
        import pandas as pd
        
        file_path = Path("data/raw/底表.xlsx")
        if not file_path.exists():
            safe_print("❌ Excel文件不存在")
            return False
        
        df = pd.read_excel(file_path, sheet_name="协议价集合")
        safe_print(f"✅ 当前数据行数: {len(df)}")
        
        if len(df) > 0:
            safe_print("最新数据:")
            latest_row = df.iloc[-1]
            safe_print(f"客户ID: {latest_row['客户ID(必填)']}")
            safe_print(f"货品ID: {latest_row['货品ID(必填)']}")
            safe_print(f"协议价: {latest_row['协议价(必填)']}")
        else:
            safe_print("📝 文件为空，等待数据写入")
        
        return True
        
    except Exception as e:
        safe_print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    safe_print("🚀 编码问题解决方案")
    safe_print("=" * 50)
    safe_print("目标: 解决 'ascii' codec can't encode characters 错误")
    safe_print("=" * 50)
    
    # 验证初始状态
    safe_print("📋 步骤1: 验证初始状态")
    verify_excel_file()
    
    # 运行编码安全测试
    safe_print("\n📋 步骤2: 运行编码安全测试")
    success = test_with_encoding_safe_agent()
    
    # 验证最终状态
    safe_print("\n📋 步骤3: 验证最终状态")
    verify_excel_file()
    
    if success:
        safe_print("\n🎉 编码问题解决方案测试完成！")
        safe_print("\n📋 解决方案总结:")
        safe_print("✅ 使用safe_print函数捕获编码错误")
        safe_print("✅ 智能体使用英文响应避免编码问题")
        safe_print("✅ 设置PYTHONIOENCODING=utf-8")
        safe_print("✅ Excel操作功能正常")
    else:
        safe_print("\n❌ 测试失败，需要进一步调试")

if __name__ == '__main__':
    main()
