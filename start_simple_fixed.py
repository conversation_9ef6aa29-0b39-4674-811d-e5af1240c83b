"""
简化修复版启动脚本
专门处理: 客户ID为002 货品ID为003 价格为3 开始执行脚本
"""
import sys
import os
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def safe_print(text):
    """安全打印，避免编码错误"""
    try:
        print(text, flush=True)
    except:
        try:
            print(str(text).encode('ascii', 'ignore').decode('ascii'), flush=True)
        except:
            print("[输出错误]", flush=True)

def check_and_prepare():
    """检查和准备环境"""
    safe_print("🔍 检查环境...")
    
    # 检查原始文件
    original_file = Path("data/raw/底表.xlsx")
    test_file = Path("data/raw/底表_测试.xlsx")
    
    safe_print(f"原始文件: {original_file} - {'存在' if original_file.exists() else '不存在'}")
    safe_print(f"测试文件: {test_file} - {'存在' if test_file.exists() else '不存在'}")
    
    # 如果测试文件不存在，尝试创建
    if not test_file.exists() and original_file.exists():
        try:
            import shutil
            shutil.copy2(original_file, test_file)
            safe_print(f"✅ 已创建测试文件: {test_file}")
        except Exception as e:
            safe_print(f"❌ 创建测试文件失败: {e}")
            return False
    
    return test_file.exists()

def process_command_directly():
    """直接处理用户指令"""
    safe_print("\n🎯 直接处理指令: 客户ID为002 货品ID为003 价格为3 开始执行脚本")
    
    try:
        import pandas as pd
        from datetime import datetime, timedelta
        
        # 参数
        customer_id = "002"
        product_id = "003"
        agreement_price = 3.0
        
        safe_print(f"📋 参数: 客户ID={customer_id}, 货品ID={product_id}, 价格={agreement_price}")
        
        # 使用测试文件
        test_file = Path("data/raw/底表_测试.xlsx")
        
        # 读取数据
        safe_print("📖 读取Excel文件...")
        df = pd.read_excel(test_file, sheet_name="协议价集合")
        safe_print(f"📊 原始数据: {len(df)} 行")
        
        # 准备新数据
        current_date = datetime.now()
        start_date = current_date.strftime("%Y/%m/%d")
        end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
        
        # 创建新行
        new_row = {}
        for col in df.columns:
            if col == "客户ID(必填)":
                new_row[col] = customer_id
            elif col == "货品ID(必填)":
                new_row[col] = product_id
            elif col == "协议价(必填)":
                new_row[col] = agreement_price
            elif col == "开始日期(必填)":
                new_row[col] = start_date
            elif col == "结束日期(必填)":
                new_row[col] = end_date
            elif col == "考核价":
                new_row[col] = agreement_price
            elif col == "建议零售价":
                new_row[col] = agreement_price
            else:
                new_row[col] = ""
        
        # 添加新行
        new_df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
        safe_print(f"📊 更新后数据: {len(new_df)} 行")
        
        # 保存文件
        safe_print("💾 保存文件...")
        
        # 读取所有工作表
        all_sheets = {}
        with pd.ExcelFile(test_file) as xls:
            for sheet_name in xls.sheet_names:
                if sheet_name == "协议价集合":
                    all_sheets[sheet_name] = new_df
                else:
                    all_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
        
        # 写入文件
        with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
            for sheet_name, sheet_data in all_sheets.items():
                sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        safe_print("✅ 协议价数据已成功写入Excel文件！")
        safe_print("")
        safe_print("📋 写入详情：")
        safe_print(f"• 文件路径：{test_file}")
        safe_print("• 工作表：协议价集合")
        safe_print(f"• 客户ID：{customer_id}")
        safe_print(f"• 货品ID：{product_id}")
        safe_print(f"• 协议价：{agreement_price}")
        safe_print(f"• 开始日期：{start_date}")
        safe_print(f"• 结束日期：{end_date}")
        safe_print(f"• 考核价：{agreement_price}")
        safe_print(f"• 建议零售价：{agreement_price}")
        safe_print("")
        safe_print(f"📊 当前工作表共有 {len(new_df)} 条记录。")
        
        return True
        
    except Exception as e:
        safe_print(f"❌ 处理失败: {e}")
        return False

def create_simple_agent():
    """创建简单的智能体"""
    safe_print("\n🤖 创建智能体...")
    
    try:
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from langchain.tools import BaseTool
        from pydantic import BaseModel, Field
        from typing import Type
        
        # 加载环境变量
        load_dotenv()
        
        # 禁用追踪
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.1
        )
        
        # 创建简单工具
        class AgreementInput(BaseModel):
            customer_id: str = Field(description="客户ID")
            product_id: str = Field(description="货品ID")
            agreement_price: float = Field(description="协议价")
        
        class SimpleAgreementTool(BaseTool):
            name: str = "process_agreement_price"
            description: str = "处理协议价数据"
            args_schema: Type[BaseModel] = AgreementInput
            
            def _run(self, customer_id: str, product_id: str, agreement_price: float) -> str:
                try:
                    import pandas as pd
                    from datetime import datetime, timedelta
                    
                    test_file = Path("data/raw/底表_测试.xlsx")
                    
                    # 读取数据
                    df = pd.read_excel(test_file, sheet_name="协议价集合")
                    
                    # 准备新数据
                    current_date = datetime.now()
                    start_date = current_date.strftime("%Y/%m/%d")
                    end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
                    
                    # 创建新行
                    new_row = {}
                    for col in df.columns:
                        if col == "客户ID(必填)":
                            new_row[col] = customer_id
                        elif col == "货品ID(必填)":
                            new_row[col] = product_id
                        elif col == "协议价(必填)":
                            new_row[col] = agreement_price
                        elif col == "开始日期(必填)":
                            new_row[col] = start_date
                        elif col == "结束日期(必填)":
                            new_row[col] = end_date
                        elif col == "考核价":
                            new_row[col] = agreement_price
                        elif col == "建议零售价":
                            new_row[col] = agreement_price
                        else:
                            new_row[col] = ""
                    
                    # 添加新行
                    new_df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
                    
                    # 保存文件
                    all_sheets = {}
                    with pd.ExcelFile(test_file) as xls:
                        for sheet_name in xls.sheet_names:
                            if sheet_name == "协议价集合":
                                all_sheets[sheet_name] = new_df
                            else:
                                all_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
                    
                    with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
                        for sheet_name, sheet_data in all_sheets.items():
                            sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    return f"成功写入协议价数据！客户ID: {customer_id}, 货品ID: {product_id}, 价格: {agreement_price}, 开始日期: {start_date}, 结束日期: {end_date}"
                    
                except Exception as e:
                    return f"写入失败: {str(e)}"
        
        # 创建工具和智能体
        tool = SimpleAgreementTool()
        
        system_prompt = """
        你是一个协议价处理助手。
        
        当用户输入包含"客户ID为"、"货品ID为"、"价格为"、"开始执行脚本"时：
        1. 提取客户ID、货品ID、价格三个参数
        2. 立即调用process_agreement_price工具
        
        示例：
        "客户ID为002 货品ID为003 价格为3 开始执行脚本"
        提取：customer_id="002", product_id="003", agreement_price=3
        
        用中文回复。
        """
        
        agent = create_react_agent(model=llm, tools=[tool], prompt=system_prompt)
        
        safe_print("✅ 智能体创建成功")
        return agent
        
    except Exception as e:
        safe_print(f"❌ 智能体创建失败: {e}")
        return None

def main():
    """主函数"""
    safe_print("🚀 Data Agent 简化修复版")
    safe_print("=" * 50)
    
    # 步骤1: 检查环境
    if not check_and_prepare():
        safe_print("❌ 环境准备失败")
        return
    
    # 步骤2: 直接处理指令
    safe_print("\n📋 步骤1: 直接处理用户指令")
    direct_success = process_command_directly()
    
    if not direct_success:
        safe_print("❌ 直接处理失败")
        return
    
    # 步骤3: 创建智能体
    safe_print("\n📋 步骤2: 创建智能体进行交互")
    agent = create_simple_agent()
    
    if not agent:
        safe_print("❌ 智能体创建失败，但直接处理成功")
        return
    
    # 步骤4: 交互模式
    safe_print("\n🎯 智能体交互模式")
    safe_print("支持的指令格式:")
    safe_print("客户ID为XXX 货品ID为XXX 价格为XXX 开始执行脚本")
    safe_print("输入 'quit' 退出")
    safe_print("-" * 50)
    
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                safe_print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            safe_print("🤖 Agent: 处理中...")
            
            try:
                response = agent.invoke({
                    "messages": [{"role": "user", "content": user_input}]
                })
                
                result = response["messages"][-1].content
                safe_print(f"🤖 Agent: {result}")
                
            except Exception as e:
                safe_print(f"❌ 处理错误: {str(e)}")
                
        except KeyboardInterrupt:
            safe_print("\n👋 再见！")
            break
        except Exception as e:
            safe_print(f"❌ 输入错误: {str(e)}")

if __name__ == '__main__':
    main()
