"""
修复智能体Excel操作问题
确保智能体正确识别和执行Excel写入操作
"""
import sys
import os
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def safe_print(text):
    """安全打印"""
    try:
        print(text, flush=True)
    except UnicodeEncodeError:
        print("[编码处理] 响应包含中文字符，但操作可能已完成", flush=True)
    except Exception:
        print("[输出错误]", flush=True)

def create_fixed_agent():
    """创建修复版智能体"""
    safe_print("🤖 创建修复版智能体...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.0  # 设置为0确保一致性
        )
        
        # 使用Excel工具
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        # 强化的系统提示词
        system_prompt = """
        You are an Excel data processing assistant. Your primary job is to detect specific trigger commands and execute Excel operations.

        CRITICAL RULE: When you see ANY text containing these patterns:
        - "客户ID为" followed by any value
        - "货品ID为" followed by any value  
        - "价格为" followed by any number
        - "开始执行脚本"

        You MUST immediately call the write_agreement_price_to_excel tool.

        EXTRACTION RULES:
        1. Find "客户ID为" and extract everything after it until comma or space
        2. Find "货品ID为" and extract everything after it until comma or space
        3. Find "价格为" and extract the number after it
        4. Call write_agreement_price_to_excel with these values

        EXAMPLES that MUST trigger the tool:
        - "客户ID为001,货品ID为002,价格为1,开始执行脚本" → customer_id="001", product_id="002", agreement_price=1
        - "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本" → customer_id="C001", product_id="P001", agreement_price=99.99

        DO NOT ask questions. DO NOT hesitate. Execute immediately when you see the pattern.
        Respond in English to avoid encoding issues.
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        safe_print("✅ 修复版智能体创建成功")
        return agent
        
    except Exception as e:
        safe_print(f"❌ 智能体创建失败: {e}")
        return None

def test_fixed_agent():
    """测试修复版智能体"""
    safe_print("\n🧪 测试修复版智能体...")
    
    agent = create_fixed_agent()
    if not agent:
        return False
    
    # 测试用例
    test_cases = [
        "客户ID为001,货品ID为002,价格为1,开始执行脚本",
        "客户ID为C005,货品ID为P005,价格为5.5,开始执行脚本"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        safe_print(f"\n🔄 测试 {i}: {test_input}")
        
        # 获取初始数据行数
        import pandas as pd
        initial_df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
        initial_count = len(initial_df)
        safe_print(f"📊 处理前数据行数: {initial_count}")
        
        try:
            response = agent.invoke({
                "messages": [{"role": "user", "content": test_input}]
            })
            
            # 检查响应
            result = response["messages"][-1].content
            safe_print(f"🤖 智能体响应: {result}")
            
        except UnicodeEncodeError:
            safe_print("⚠️ 编码错误，但继续检查数据...")
        except Exception as e:
            safe_print(f"❌ 处理错误: {e}")
        
        # 检查数据是否增加
        try:
            final_df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
            final_count = len(final_df)
            safe_print(f"📊 处理后数据行数: {final_count}")
            
            if final_count > initial_count:
                safe_print("✅ 数据成功写入Excel文件")
                new_row = final_df.iloc[-1]
                safe_print(f"新增数据: 客户ID={new_row['客户ID(必填)']}, 货品ID={new_row['货品ID(必填)']}, 价格={new_row['协议价(必填)']}")
            else:
                safe_print("❌ 数据未写入Excel文件")
        except Exception as e:
            safe_print(f"❌ 检查数据失败: {e}")
    
    return True

def interactive_mode():
    """交互模式"""
    safe_print("\n🎯 交互模式")
    safe_print("=" * 50)
    safe_print("输入触发指令测试智能体")
    safe_print("格式: 客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本")
    safe_print("输入 'quit' 退出")
    safe_print("-" * 50)
    
    agent = create_fixed_agent()
    if not agent:
        return
    
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                safe_print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            safe_print("🤖 Agent: 处理中...")
            
            # 获取初始数据行数
            import pandas as pd
            initial_df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
            initial_count = len(initial_df)
            
            try:
                response = agent.invoke({
                    "messages": [{"role": "user", "content": user_input}]
                })
                
                result = response["messages"][-1].content
                safe_print(f"🤖 Agent: {result}")
                
            except UnicodeEncodeError:
                safe_print("⚠️ 响应包含特殊字符，但处理可能已完成")
            except Exception as e:
                safe_print(f"❌ 处理错误: {e}")
            
            # 检查数据变化
            try:
                final_df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
                final_count = len(final_df)
                
                if final_count > initial_count:
                    safe_print(f"✅ 数据已写入！当前共 {final_count} 条记录")
                else:
                    safe_print(f"📊 数据未变化，当前共 {final_count} 条记录")
            except Exception as e:
                safe_print(f"❌ 检查数据失败: {e}")
            
        except KeyboardInterrupt:
            safe_print("\n👋 再见！")
            break
        except Exception as e:
            safe_print(f"❌ 输入错误: {e}")

def main():
    """主函数"""
    safe_print("🚀 修复智能体Excel操作")
    safe_print("=" * 50)
    safe_print("目标: 确保智能体正确识别和执行Excel写入")
    safe_print("=" * 50)
    
    # 测试修复版智能体
    success = test_fixed_agent()
    
    if success:
        safe_print("\n🎉 修复测试完成！")
        safe_print("\n选择模式:")
        safe_print("1. 进入交互模式")
        safe_print("2. 退出")
        
        choice = input("\n请选择 (1/2): ").strip()
        if choice == "1":
            interactive_mode()
    else:
        safe_print("\n❌ 修复测试失败")

if __name__ == '__main__':
    main()
