"""
调试智能体Excel操作
找出为什么智能体没有正确调用Excel工具
"""
import sys
import os
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def safe_print(text):
    """安全打印"""
    try:
        print(text, flush=True)
    except UnicodeEncodeError:
        print("[编码处理] 响应包含中文字符", flush=True)
    except Exception:
        print("[输出错误]", flush=True)

def debug_agent_behavior():
    """调试智能体行为"""
    safe_print("🔍 调试智能体Excel操作...")
    
    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        # 加载环境变量
        load_dotenv()
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        
        # 创建LLM
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.1
        )
        
        # 使用Excel工具
        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]
        
        safe_print(f"✅ Excel工具名称: {excel_tool.name}")
        safe_print(f"✅ Excel工具描述: {excel_tool.description}")
        
        # 详细的系统提示词
        system_prompt = """
        You are a helpful assistant that processes agreement price data.
        
        CRITICAL INSTRUCTIONS:
        When you see the EXACT format: "客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本"
        You MUST immediately call the write_agreement_price_to_excel tool with these parameters:
        - customer_id: extract the value after "客户ID为"
        - product_id: extract the value after "货品ID为"  
        - agreement_price: extract the numeric value after "价格为"
        
        DO NOT ask for confirmation. Execute immediately.
        
        Examples that should trigger the tool:
        - "客户ID为001,货品ID为002,价格为1,开始执行脚本"
        - "客户ID为C001,货品ID为P001,价格为99.99,开始执行脚本"
        
        Always respond in English to avoid encoding issues.
        """
        
        # 创建智能体
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt
        )
        
        safe_print("✅ 智能体创建成功")
        
        # 测试触发指令
        test_input = "客户ID为001,货品ID为002,价格为1,开始执行脚本"
        
        safe_print(f"\n🔄 测试输入: {test_input}")
        safe_print("🤖 智能体处理中...")
        
        # 获取初始数据行数
        import pandas as pd
        initial_df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
        initial_count = len(initial_df)
        safe_print(f"📊 处理前数据行数: {initial_count}")
        
        try:
            response = agent.invoke({
                "messages": [{"role": "user", "content": test_input}]
            })
            
            # 检查响应
            result = response["messages"][-1].content
            safe_print(f"🤖 智能体响应: {result}")
            
            # 检查数据是否增加
            final_df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
            final_count = len(final_df)
            safe_print(f"📊 处理后数据行数: {final_count}")
            
            if final_count > initial_count:
                safe_print("✅ 数据成功写入Excel文件")
                new_row = final_df.iloc[-1]
                safe_print(f"新增数据: 客户ID={new_row['客户ID(必填)']}, 货品ID={new_row['货品ID(必填)']}, 价格={new_row['协议价(必填)']}")
            else:
                safe_print("❌ 数据未写入Excel文件")
                safe_print("可能原因: 智能体没有调用Excel工具")
            
        except UnicodeEncodeError:
            safe_print("⚠️ 编码错误，检查数据是否写入...")
            final_df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
            final_count = len(final_df)
            safe_print(f"📊 处理后数据行数: {final_count}")
            
            if final_count > initial_count:
                safe_print("✅ 尽管有编码错误，数据已成功写入")
            else:
                safe_print("❌ 编码错误且数据未写入")
        
        except Exception as e:
            safe_print(f"❌ 处理失败: {e}")
        
        return True
        
    except Exception as e:
        safe_print(f"❌ 调试失败: {e}")
        return False

def test_direct_tool_call():
    """直接测试工具调用"""
    safe_print("\n🔧 直接测试Excel工具...")
    
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        tool = create_excel_writer_tool()
        
        # 获取初始数据行数
        import pandas as pd
        initial_df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
        initial_count = len(initial_df)
        safe_print(f"📊 直接调用前数据行数: {initial_count}")
        
        # 直接调用工具
        result = tool._run(
            customer_id="003",
            product_id="004", 
            agreement_price=2.0
        )
        
        safe_print("✅ 工具直接调用成功")
        safe_print(f"结果: {result}")
        
        # 检查数据
        final_df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
        final_count = len(final_df)
        safe_print(f"📊 直接调用后数据行数: {final_count}")
        
        return True
        
    except Exception as e:
        safe_print(f"❌ 直接工具调用失败: {e}")
        return False

def main():
    """主函数"""
    safe_print("🚀 调试智能体Excel操作")
    safe_print("=" * 50)
    
    # 测试1: 直接工具调用
    safe_print("📋 测试1: 直接工具调用")
    success1 = test_direct_tool_call()
    
    # 测试2: 智能体调用
    safe_print("\n📋 测试2: 智能体调用")
    success2 = debug_agent_behavior()
    
    safe_print(f"\n📋 调试总结:")
    safe_print(f"✅ 直接工具调用: {'成功' if success1 else '失败'}")
    safe_print(f"✅ 智能体调用: {'成功' if success2 else '失败'}")
    
    if success1 and not success2:
        safe_print("\n🔍 问题分析:")
        safe_print("• Excel工具本身正常工作")
        safe_print("• 问题出现在智能体没有正确调用工具")
        safe_print("• 可能是指令格式识别问题")
        safe_print("• 建议检查系统提示词和指令匹配逻辑")

if __name__ == '__main__':
    main()
