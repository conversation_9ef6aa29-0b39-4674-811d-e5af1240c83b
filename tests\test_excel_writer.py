"""
测试Excel写入工具的功能
"""
import os
import sys
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta

# 设置编码环境
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from data_agent.tools.excel_writer import create_excel_writer_tool


def test_excel_writer_tool():
    """测试Excel写入工具"""
    print("🧪 开始测试Excel写入工具...")
    
    # 创建工具实例
    tool = create_excel_writer_tool()
    
    # 测试数据
    test_cases = [
        {
            "customer_id": "C001",
            "product_id": "P001",
            "agreement_price": 99.99,
            "description": "第一条测试数据"
        },
        {
            "customer_id": "C002", 
            "product_id": "P002",
            "agreement_price": 150.50,
            "description": "第二条测试数据"
        },
        {
            "customer_id": "C003",
            "product_id": "P003", 
            "agreement_price": 75.25,
            "description": "第三条测试数据"
        }
    ]
    
    print(f"\n📋 准备测试 {len(test_cases)} 条数据...")
    
    # 清理之前的测试文件
    test_file = Path("data/raw/底表.xlsx")
    if test_file.exists():
        try:
            os.remove(test_file)
            print(f"🗑️ 已删除之前的测试文件: {test_file}")
        except Exception as e:
            print(f"⚠️ 删除测试文件失败: {e}")
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔄 执行测试用例 {i}: {test_case['description']}")
        
        result = tool._run(
            customer_id=test_case["customer_id"],
            product_id=test_case["product_id"],
            agreement_price=test_case["agreement_price"]
        )
        
        print(f"📤 测试结果:")
        print(result)
        
        # 验证文件是否创建
        if test_file.exists():
            print(f"✅ 文件创建成功: {test_file}")
        else:
            print(f"❌ 文件创建失败: {test_file}")
            continue
    
    # 验证最终结果
    print(f"\n🔍 验证最终结果...")
    
    if test_file.exists():
        try:
            # 读取Excel文件
            df = pd.read_excel(test_file, sheet_name="协议价集合")
            
            print(f"📊 Excel文件验证结果:")
            print(f"• 工作表名称: 协议价集合")
            print(f"• 数据行数: {len(df)}")
            print(f"• 列数: {len(df.columns)}")
            print(f"• 列名: {list(df.columns)}")
            
            print(f"\n📋 数据内容预览:")
            print(df.to_string(index=False))
            
            # 验证数据完整性
            expected_columns = ["客户ID", "货品ID", "协议价", "开始日期", "结束日期", "考核价", "建议零售价"]
            missing_columns = [col for col in expected_columns if col not in df.columns]
            
            if missing_columns:
                print(f"❌ 缺少列: {missing_columns}")
            else:
                print(f"✅ 所有必需列都存在")
            
            # 验证数据类型和值
            for index, row in df.iterrows():
                print(f"\n🔍 验证第 {index + 1} 行数据:")
                print(f"• 客户ID: {row['客户ID']}")
                print(f"• 货品ID: {row['货品ID']}")
                print(f"• 协议价: {row['协议价']}")
                print(f"• 开始日期: {row['开始日期']}")
                print(f"• 结束日期: {row['结束日期']}")
                print(f"• 考核价: {row['考核价']}")
                print(f"• 建议零售价: {row['建议零售价']}")
                
                # 验证价格字段是否正确复制
                if row['考核价'] == row['协议价'] and row['建议零售价'] == row['协议价']:
                    print(f"✅ 价格字段复制正确")
                else:
                    print(f"❌ 价格字段复制错误")
                
                # 验证日期格式
                try:
                    start_date = datetime.strptime(row['开始日期'], "%Y/%m/%d")
                    end_date = datetime.strptime(row['结束日期'], "%Y/%m/%d")
                    
                    if (end_date - start_date).days == 30:
                        print(f"✅ 日期计算正确（相差30天）")
                    else:
                        print(f"❌ 日期计算错误（相差{(end_date - start_date).days}天）")
                        
                except ValueError as e:
                    print(f"❌ 日期格式错误: {e}")
            
            print(f"\n🎉 Excel写入工具测试完成！")
            
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {e}")
    else:
        print(f"❌ 测试文件不存在: {test_file}")


def safe_print(text):
    """安全打印，避免编码错误"""
    try:
        print(text, flush=True)
    except UnicodeEncodeError:
        # 如果编码失败，使用ASCII安全模式
        safe_text = str(text).encode('ascii', 'ignore').decode('ascii')
        print(safe_text, flush=True)
    except Exception:
        print("[输出编码错误]", flush=True)


def test_agent_integration():
    """测试智能体集成"""
    safe_print(f"\n🤖 测试智能体集成...")

    try:
        from data_agent.agents.graph import get_agent_executor

        agent = get_agent_executor()

        # 测试协议价数据输入
        test_input = """
        请帮我处理以下协议价数据：
        客户ID: C999
        货品ID: P999
        协议价: 188.88
        """

        safe_print(f"📤 发送测试请求:")
        safe_print(test_input)

        response = agent.invoke({
            "messages": [{"role": "user", "content": test_input}]
        })

        safe_print(f"\n📥 智能体响应:")
        try:
            result = response["messages"][-1].content
            safe_print(result)
        except UnicodeEncodeError:
            safe_print("响应包含中文字符，但处理可能已完成。请检查Excel文件。")

        # 验证文件是否更新
        test_file = Path("data/raw/底表.xlsx")
        if test_file.exists():
            df = pd.read_excel(test_file, sheet_name="协议价集合")
            safe_print(f"\n📊 更新后的数据行数: {len(df)}")

            # 查找新添加的数据
            new_row = df[df['客户ID'] == 'C999']
            if not new_row.empty:
                safe_print(f"✅ 智能体成功添加了新数据:")
                safe_print(new_row.to_string(index=False))
            else:
                safe_print(f"❌ 未找到智能体添加的数据")

        safe_print(f"\n🎉 智能体集成测试完成！")

    except Exception as e:
        safe_print(f"❌ 智能体集成测试失败: {e}")


def test_non_trigger_commands():
    """测试非触发指令（应该不会执行Excel操作）"""
    safe_print(f"\n🔍 测试非触发指令...")

    try:
        import os
        from dotenv import load_dotenv
        from langgraph.prebuilt import create_react_agent
        from langchain_deepseek import ChatDeepSeek
        from data_agent.tools.excel_writer import create_excel_writer_tool

        # 设置编码环境
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # 加载环境变量
        load_dotenv()
        os.environ["LANGCHAIN_TRACING_V2"] = "false"

        # 创建智能体（简化版）
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.2
        )

        excel_tool = create_excel_writer_tool()
        tools = [excel_tool]

        system_prompt = """
        You are a helpful assistant. Only execute Excel operations when you see the exact format:
        "客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本"

        For other requests, just respond normally without using tools.
        Always respond in Chinese.
        """

        agent = create_react_agent(model=llm, tools=tools, prompt=system_prompt)

        # 测试非触发指令
        non_trigger_commands = [
            "你好，请介绍一下你的功能",
            "客户ID是C001，货品ID是P001，价格是99.99",  # 格式不完全匹配
            "请帮我处理协议价数据：客户ID为C001，货品ID为P001，价格为99.99",  # 不是精确格式
        ]

        for i, command in enumerate(non_trigger_commands, 1):
            safe_print(f"\n🔄 非触发测试 {i}:")
            safe_print(f"💬 输入: {command}")
            safe_print(f"🤖 响应:")
            safe_print("-" * 30)

            try:
                response = agent.invoke({
                    "messages": [{"role": "user", "content": command}]
                })

                result = response["messages"][-1].content
                safe_print(result)
                safe_print("-" * 30)

            except UnicodeEncodeError as e:
                safe_print(f"❌ 响应失败: 'ascii' codec can't encode characters in position 47-54: ordinal not in range(128)")
                safe_print("这是编码问题，但智能体可能已正常响应。")
            except Exception as e:
                safe_print(f"❌ 响应失败: {e}")

        return True

    except Exception as e:
        safe_print(f"❌ 非触发测试失败: {e}")
        return False


if __name__ == '__main__':
    # 设置编码
    import os
    import sys
    os.environ['PYTHONIOENCODING'] = 'utf-8'

    safe_print("🚀 开始Excel写入工具完整测试...")

    # 测试Excel写入工具
    test_excel_writer_tool()

    # 测试智能体集成
    test_agent_integration()

    # 测试非触发指令
    test_non_trigger_commands()

    safe_print(f"\n✨ 所有测试完成！")
