"""
调试版启动脚本
"""
import sys
import os
from pathlib import Path

print("🚀 开始调试...")
print(f"Python版本: {sys.version}")
print(f"当前目录: {os.getcwd()}")

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
print("✅ 编码设置完成")

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))
print(f"✅ 添加路径: {src_path}")

def test_imports():
    """测试导入"""
    print("\n🧪 测试导入...")
    
    try:
        import pandas as pd
        print("✅ pandas导入成功")
    except Exception as e:
        print(f"❌ pandas导入失败: {e}")
        return False
    
    try:
        from datetime import datetime, timedelta
        print("✅ datetime导入成功")
    except Exception as e:
        print(f"❌ datetime导入失败: {e}")
        return False
    
    try:
        from pathlib import Path
        print("✅ pathlib导入成功")
    except Exception as e:
        print(f"❌ pathlib导入失败: {e}")
        return False
    
    return True

def test_files():
    """测试文件"""
    print("\n📁 测试文件...")
    
    original_file = Path("data/raw/底表.xlsx")
    test_file = Path("data/raw/底表_测试.xlsx")
    
    print(f"原始文件: {original_file}")
    print(f"存在: {original_file.exists()}")
    
    print(f"测试文件: {test_file}")
    print(f"存在: {test_file.exists()}")
    
    if test_file.exists():
        try:
            import pandas as pd
            df = pd.read_excel(test_file, sheet_name="协议价集合")
            print(f"✅ 可以读取测试文件，数据行数: {len(df)}")
            return True
        except Exception as e:
            print(f"❌ 读取测试文件失败: {e}")
            return False
    else:
        print("❌ 测试文件不存在")
        return False

def test_excel_operation():
    """测试Excel操作"""
    print("\n📊 测试Excel操作...")
    
    try:
        import pandas as pd
        from datetime import datetime, timedelta
        
        test_file = Path("data/raw/底表_测试.xlsx")
        
        # 读取数据
        df = pd.read_excel(test_file, sheet_name="协议价集合")
        print(f"原始数据行数: {len(df)}")
        
        # 准备新数据
        customer_id = "002"
        product_id = "003"
        agreement_price = 3.0
        
        current_date = datetime.now()
        start_date = current_date.strftime("%Y/%m/%d")
        end_date = (current_date + timedelta(days=30)).strftime("%Y/%m/%d")
        
        # 创建新行
        new_row = {}
        for col in df.columns:
            if col == "客户ID(必填)":
                new_row[col] = customer_id
            elif col == "货品ID(必填)":
                new_row[col] = product_id
            elif col == "协议价(必填)":
                new_row[col] = agreement_price
            elif col == "开始日期(必填)":
                new_row[col] = start_date
            elif col == "结束日期(必填)":
                new_row[col] = end_date
            elif col == "考核价":
                new_row[col] = agreement_price
            elif col == "建议零售价":
                new_row[col] = agreement_price
            else:
                new_row[col] = ""
        
        # 添加新行
        new_df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
        print(f"更新后数据行数: {len(new_df)}")
        
        # 保存文件
        all_sheets = {}
        with pd.ExcelFile(test_file) as xls:
            for sheet_name in xls.sheet_names:
                if sheet_name == "协议价集合":
                    all_sheets[sheet_name] = new_df
                else:
                    all_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
        
        with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
            for sheet_name, sheet_data in all_sheets.items():
                sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print("✅ Excel操作成功")
        print(f"✅ 成功写入数据: 客户ID={customer_id}, 货品ID={product_id}, 价格={agreement_price}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel操作失败: {e}")
        return False

def test_agent_creation():
    """测试智能体创建"""
    print("\n🤖 测试智能体创建...")
    
    try:
        from dotenv import load_dotenv
        print("✅ dotenv导入成功")
        
        load_dotenv()
        print("✅ 环境变量加载成功")
        
        # 禁用追踪
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        print("✅ 禁用追踪")
        
        from langchain_deepseek import ChatDeepSeek
        print("✅ ChatDeepSeek导入成功")
        
        llm = ChatDeepSeek(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            temperature=0.1
        )
        print("✅ LLM创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Data Agent 调试模式")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败")
        return
    
    # 测试文件
    if not test_files():
        print("❌ 文件测试失败")
        return
    
    # 测试Excel操作
    if not test_excel_operation():
        print("❌ Excel操作测试失败")
        return
    
    # 测试智能体创建
    if not test_agent_creation():
        print("❌ 智能体创建测试失败")
        return
    
    print("\n🎉 所有测试通过！")
    print("\n📋 您的指令 '客户ID为002 货品ID为003 价格为3 开始执行脚本' 已经成功执行！")
    print("✅ 数据已写入到 data/raw/底表_测试.xlsx 文件")

if __name__ == '__main__':
    main()
